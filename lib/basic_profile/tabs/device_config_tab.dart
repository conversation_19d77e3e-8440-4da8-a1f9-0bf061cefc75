import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../providers/app_provider.dart';
import '../../models/user.dart';

class DeviceConfigTab extends StatefulWidget {
  const DeviceConfigTab({Key? key}) : super(key: key);

  @override
  _DeviceConfigTabState createState() => _DeviceConfigTabState();
}

class _DeviceConfigTabState extends State<DeviceConfigTab> {
  // Local state for optimistic updates
  Map<String, bool> _localToggleStates = {};

  // Store recent device responses
  List<String> _deviceResponses = [];

  // Stream subscription for raw MQTT messages
  StreamSubscription<String>? _rawMessageSubscription;

  // Track last received message to avoid duplicates
  String? _lastReceivedMessage;
  DateTime? _lastMessageTime;

  @override
  void initState() {
    super.initState();
    _listenToMqttMessages();
  }

  void _listenToMqttMessages() {
    // Listen to MQTT messages from AppProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupMqttListener();
    });
  }

  void _setupMqttListener() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);

    // Retry mechanism in case MQTT handler isn't ready yet
    for (int i = 0; i < 5; i++) {
      if (appProvider.mqttHandler != null) {
        // Listen to device status updates which contain the raw messages
        appProvider.addListener(_onDeviceStatusChanged);

        // Subscribe to raw MQTT messages
        _rawMessageSubscription =
            appProvider.rawMessageStream?.listen((rawMessage) {
          if (mounted) {
            _addDeviceResponse(rawMessage);
          }
        });
        break;
      } else {
        await Future.delayed(Duration(milliseconds: 500));
      }
    }
  }

  void _onDeviceStatusChanged() {
    // This will be called when device status updates
    // For now, we'll simulate device responses
    // In a real implementation, you'd capture the actual MQTT payload here
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    if (appProvider.ds.online) {
      // Simulate a device response when status changes
      _addDeviceResponse(
          '{"status":"Device status updated","online":${appProvider.ds.online}}');
    }
  }

  void _addDeviceResponse(String response) {
    final now = DateTime.now();

    // Filter out duplicate messages within 2 seconds
    if (_lastReceivedMessage == response &&
        _lastMessageTime != null &&
        now.difference(_lastMessageTime!).inSeconds < 2) {
      return; // Skip duplicate
    }

    // Filter out non-JSON responses or empty responses
    if (response.trim().isEmpty || !response.trim().startsWith('{')) {
      return;
    }

    _lastReceivedMessage = response;
    _lastMessageTime = now;

    setState(() {
      _deviceResponses.insert(0, response); // Add to beginning
      if (_deviceResponses.length > 20) {
        _deviceResponses.removeLast(); // Keep only last 20 messages
      }
    });
  }

  @override
  void dispose() {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    appProvider.removeListener(_onDeviceStatusChanged);
    _rawMessageSubscription?.cancel();
    super.dispose();
  }

  /// Formats device number to show only last 6 digits with asterisks for previous digits
  String _formatDeviceNumber(String deviceNumber) {
    if (deviceNumber.isEmpty) return 'N/A';

    if (deviceNumber.length <= 6) {
      return deviceNumber;
    }

    // Show asterisks for all but last 6 digits
    final hiddenLength = deviceNumber.length - 6;
    final asterisks = '*' * hiddenLength;
    final lastSixDigits = deviceNumber.substring(deviceNumber.length - 6);

    return '$asterisks$lastSixDigits';
  }

  /// Formats renter field - shows "Pool Broker: Байхгүй" if empty, otherwise shows the renter domain name
  String _formatRenterInfo(String renter) {
    if (renter.isEmpty) {
      return 'Pool Broker: Байхгүй';
    }
    return 'Pool Broker: $renter';
  }

  /// Formats firmware version for display
  String _formatFirmwareVersion(String version) {
    if (version.isEmpty) {
      return 'Firmware хувилбар: N/A';
    }
    return 'Firmware хувилбар: $version';
  }

  /// Builds a compact toggle button widget
  Widget _buildToggleButton(
    BuildContext context,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Expanded(
      child: Container(
        padding: EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: FlutterFlowTheme.of(context).bodyText2.override(
                    fontFamily: 'Roboto',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: FlutterFlowTheme.of(context).secondaryColor,
              inactiveThumbColor: Colors.grey,
              inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
      ),
    );
  }

  /// Gets the current toggle state (local override or device status)
  bool _getToggleState(String commandType, bool deviceValue) {
    return _localToggleStates[commandType] ?? deviceValue;
  }

  /// Sends toggle command to device with optimistic update
  Future<void> _sendToggleCommand(
    BuildContext context,
    AppProvider appProvider,
    User? user,
    bool value,
    String commandType,
  ) async {
    // Optimistically update local state
    setState(() {
      _localToggleStates[commandType] = value;
    });

    if (user != null && appProvider.mqttHandler != null) {
      try {
        String command = value ? '${commandType}_on' : '${commandType}_off';
        bool result =
            await appProvider.mqttHandler!.sendDeviceCommand(user, command);

        if (result) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${commandType.replaceAll('_', ' ')} ${value ? 'enabled' : 'disabled'} successfully'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
          print('$commandType command sent successfully: $command');

          // Clear local state after successful command - let device status take over
          Future.delayed(Duration(seconds: 2), () {
            if (mounted) {
              setState(() {
                _localToggleStates.remove(commandType);
              });
            }
          });
        } else {
          // Revert optimistic update on failure
          setState(() {
            _localToggleStates.remove(commandType);
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to change ${commandType.replaceAll('_', ' ')} setting'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
          print('Failed to send $commandType command');
        }
      } catch (e) {
        // Revert optimistic update on error
        setState(() {
          _localToggleStates.remove(commandType);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Error changing ${commandType.replaceAll('_', ' ')} setting: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
        print('Error sending $commandType command: $e');
      }
    } else {
      // Revert optimistic update if no connection
      setState(() {
        _localToggleStates.remove(commandType);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Device not connected'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      print('User or MQTT handler is null');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final user = appProvider.authClient;
        final deviceStatus = appProvider.ds;

        // Find the default device from user's devices list
        String deviceNumber = '';
        String renter = '';
        if (user?.devices != null && user!.devices!.isNotEmpty) {
          final defaultDevice = user.devices!.firstWhere(
            (device) => device.isDefault == true,
            orElse: () =>
                user.devices!.first, // Fallback to first device if no default
          );
          deviceNumber = defaultDevice.deviceNumber;
          renter = defaultDevice.renter;
        }

        final firmwareVersion = deviceStatus.ver;
        final formattedDeviceNumber = _formatDeviceNumber(deviceNumber);
        final formattedRenterInfo = _formatRenterInfo(renter);
        final formattedFirmwareVersion =
            _formatFirmwareVersion(firmwareVersion);

        return Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                // Device Number Display at the top
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).alternate,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Device Number',
                          style: FlutterFlowTheme.of(context)
                              .bodyText2
                              .override(
                                fontFamily: 'Roboto',
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                              ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          formattedDeviceNumber,
                          style: FlutterFlowTheme.of(context).title3.override(
                                fontFamily: 'Roboto',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 1.2,
                              ),
                        ),
                        SizedBox(height: 12),
                        Text(
                          formattedRenterInfo,
                          style: FlutterFlowTheme.of(context)
                              .bodyText1
                              .override(
                                fontFamily: 'Roboto',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                        ),
                        SizedBox(height: 12),
                        // Firmware version with update button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                formattedFirmwareVersion,
                                style: FlutterFlowTheme.of(context)
                                    .bodyText1
                                    .override(
                                      fontFamily: 'Roboto',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                    ),
                              ),
                            ),
                            SizedBox(width: 12),
                            // Update and Restart buttons
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ElevatedButton(
                                  onPressed: () async {
                                    // Send update command to device
                                    if (user != null &&
                                        appProvider.mqttHandler != null) {
                                      try {
                                        bool result = await appProvider
                                            .mqttHandler!
                                            .sendDeviceCommand(user, 'update');

                                        if (result) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Update command sent successfully'),
                                              backgroundColor: Colors.green,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Update command sent successfully');
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Failed to send update command'),
                                              backgroundColor: Colors.red,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Failed to send update command');
                                        }
                                      } catch (e) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'Error sending update command: $e'),
                                            backgroundColor: Colors.red,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                        print(
                                            'Error sending update command: $e');
                                      }
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text('Device not connected'),
                                          backgroundColor: Colors.orange,
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                      print('User or MQTT handler is null');
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        FlutterFlowTheme.of(context)
                                            .secondaryColor,
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        12, 8, 12, 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: Text(
                                    'Update',
                                    style: TextStyle(
                                      fontFamily: 'Roboto',
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8),
                                ElevatedButton(
                                  onPressed: () async {
                                    // Send restart command to device
                                    if (user != null &&
                                        appProvider.mqttHandler != null) {
                                      try {
                                        bool result = await appProvider
                                            .mqttHandler!
                                            .sendDeviceCommand(user, 'restart');

                                        if (result) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Restart command sent successfully'),
                                              backgroundColor: Colors.green,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Restart command sent successfully');
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Failed to send restart command'),
                                              backgroundColor: Colors.red,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Failed to send restart command');
                                        }
                                      } catch (e) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'Error sending restart command: $e'),
                                            backgroundColor: Colors.red,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                        print(
                                            'Error sending restart command: $e');
                                      }
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text('Device not connected'),
                                          backgroundColor: Colors.orange,
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                      print('User or MQTT handler is null');
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        12, 8, 12, 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: Text(
                                    'Restart',
                                    style: TextStyle(
                                      fontFamily: 'Roboto',
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        // Sound toggle button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Sound',
                              style: FlutterFlowTheme.of(context)
                                  .bodyText1
                                  .override(
                                    fontFamily: 'Roboto',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: FlutterFlowTheme.of(context)
                                        .primaryText,
                                  ),
                            ),
                            Switch(
                              value: _getToggleState(
                                  'sound', deviceStatus.rel2 == 1),
                              onChanged: (bool value) async {
                                await _sendToggleCommand(
                                    context, appProvider, user, value, 'sound');
                              },
                              activeColor:
                                  FlutterFlowTheme.of(context).secondaryColor,
                              inactiveThumbColor: Colors.grey,
                              inactiveTrackColor:
                                  Colors.grey.withValues(alpha: 0.3),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        // Additional toggle buttons in grid layout
                        Column(
                          children: [
                            // Row 1: Key, Automatic Shutdown, Key Controlled
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildToggleButton(
                                  context,
                                  'Key',
                                  _getToggleState('key', deviceStatus.unlock),
                                  (value) => _sendToggleCommand(
                                      context, appProvider, user, value, 'key'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'Auto Shutdown',
                                  _getToggleState(
                                      'auto_shutdown', deviceStatus.sta == 1),
                                  (value) => _sendToggleCommand(
                                      context,
                                      appProvider,
                                      user,
                                      value,
                                      'auto_shutdown'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'Key Controlled',
                                  _getToggleState(
                                      'key_controlled', deviceStatus.start),
                                  (value) => _sendToggleCommand(
                                      context,
                                      appProvider,
                                      user,
                                      value,
                                      'key_controlled'),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            // Row 2: Voltage Notify, GPS, BLE
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildToggleButton(
                                  context,
                                  'Voltage Notify',
                                  _getToggleState('voltage_notify',
                                      deviceStatus.volt > 12.0),
                                  (value) => _sendToggleCommand(
                                      context,
                                      appProvider,
                                      user,
                                      value,
                                      'voltage_notify'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'GPS',
                                  _getToggleState('gps', deviceStatus.gps),
                                  (value) => _sendToggleCommand(
                                      context, appProvider, user, value, 'gps'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'BLE',
                                  _getToggleState('ble', deviceStatus.ble),
                                  (value) => _sendToggleCommand(
                                      context, appProvider, user, value, 'ble'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Title
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      'device_config_placeholder' /* Device Configuration */,
                    ),
                    style: FlutterFlowTheme.of(context).title2,
                  ),
                ),

                // Device Response Window
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 24),
                  child: Container(
                    width: double.infinity,
                    height: 200,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).alternate,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          width: double.infinity,
                          padding:
                              EdgeInsetsDirectional.fromSTEB(16, 12, 16, 8),
                          decoration: BoxDecoration(
                            color:
                                FlutterFlowTheme.of(context).primaryBackground,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Device Response',
                                style: FlutterFlowTheme.of(context)
                                    .bodyText1
                                    .override(
                                      fontFamily: 'Roboto',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                    ),
                              ),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Device: ${_formatDeviceNumber(user?.device?.deviceNumber ?? '')}',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyText2
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: FlutterFlowTheme.of(context)
                                              .secondaryText,
                                        ),
                                  ),
                                  SizedBox(width: 8),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        _deviceResponses.clear();
                                      });
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content:
                                              Text('Response history cleared'),
                                          backgroundColor: Colors.blue,
                                          duration: Duration(seconds: 1),
                                        ),
                                      );
                                    },
                                    child: Container(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          6, 4, 6, 4),
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.red.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color:
                                              Colors.red.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.clear,
                                            size: 12,
                                            color: Colors.red[700],
                                          ),
                                          SizedBox(width: 2),
                                          Text(
                                            'Clear',
                                            style: TextStyle(
                                              fontFamily: 'Roboto',
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.red[700],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Response content
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            padding:
                                EdgeInsetsDirectional.fromSTEB(16, 8, 16, 16),
                            child: _deviceResponses.isEmpty
                                ? Center(
                                    child: Text(
                                      'No device responses yet.\nSend a command to see responses here.',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontFamily: 'Roboto',
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: _deviceResponses.length,
                                    itemBuilder: (context, index) {
                                      final response = _deviceResponses[index];
                                      final timestamp = DateTime.now()
                                          .subtract(Duration(minutes: index))
                                          .toString()
                                          .substring(11, 19);

                                      return Container(
                                        margin: EdgeInsetsDirectional.fromSTEB(
                                            0, 2, 0, 2),
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8, 6, 8, 6),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[50],
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          border: Border.all(
                                            color: Colors.grey[300]!,
                                            width: 0.5,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              timestamp,
                                              style: TextStyle(
                                                fontFamily: 'Roboto',
                                                fontSize: 10,
                                                color: Colors.grey[600],
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            SizedBox(height: 2),
                                            Text(
                                              response,
                                              style: TextStyle(
                                                fontFamily: 'Courier',
                                                fontSize: 11,
                                                color: Colors.black87,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
